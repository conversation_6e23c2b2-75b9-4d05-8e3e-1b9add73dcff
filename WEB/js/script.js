
let employees = [];
let filteredEmployees = [];

// Funkce pro řazení zaměstnanců podle příjmení
function sortEmployeesAlphabetically(employeeList) {
    return employeeList.sort((a, b) => {
        // Získáme příjmení (prv<PERSON><PERSON>, proto<PERSON><PERSON> form<PERSON> "Příjmení Jméno")
        const lastNameA = removeDiacritics(a.jmeno.trim().split(' ')[0].toLowerCase());
        const lastNameB = removeDiacritics(b.jmeno.trim().split(' ')[0].toLowerCase());
        return lastNameA.localeCompare(lastNameB, 'cs', { numeric: true });
    });
}

// Funkce pro hierarchické řazení zaměstnanců
function sortEmployeesHierarchically(employeeList) {
    return employeeList.sort((a, b) => {
        const isManagerA = a.pozice.toLowerCase().includes('vedoucí') ||
                          a.pozice.toLowerCase().includes('ředitel') ||
                          a.pozice.toLowerCase().includes('předseda') ||
                          a.pozice.toLowerCase().includes('místopředseda');
        const isManagerB = b.pozice.toLowerCase().includes('vedoucí') ||
                          b.pozice.toLowerCase().includes('ředitel') ||
                          b.pozice.toLowerCase().includes('předseda') ||
                          b.pozice.toLowerCase().includes('místopředseda');

        // Pokud jeden je vedoucí a druhý ne, vedoucí má přednost
        if (isManagerA && !isManagerB) return -1;
        if (!isManagerA && isManagerB) return 1;

        // Pokud jsou oba vedoucí nebo oba specialisté, řadíme podle příjmení
        const lastNameA = removeDiacritics(a.jmeno.trim().split(' ')[0].toLowerCase());
        const lastNameB = removeDiacritics(b.jmeno.trim().split(' ')[0].toLowerCase());
        return lastNameA.localeCompare(lastNameB, 'cs', { numeric: true });
    });
}

// Funkce pro inicializaci bočního panelu oddělení v directory
function initializeDirectoryDepartmentsPanel() {
    if (!employees || employees.length === 0) {
        console.log('Data zaměstnanců nejsou k dispozici pro directory panel');
        return;
    }

    const departmentsList = document.getElementById('departmentsListDirectory');
    if (!departmentsList) {
        console.log('Element departmentsListDirectory nenalezen');
        return;
    }

    // Získáme všechna oddělení a spočítáme zaměstnance
    const departments = {};
    employees.forEach(employee => {
        const deptString = employee.oddeleni;
        // Rozdělíme oddělení podle čárky (pro zaměstnance ve více odděleních)
        const depts = deptString.split(',').map(d => d.trim());

        depts.forEach(dept => {
            if (!departments[dept]) {
                departments[dept] = [];
            }
            departments[dept].push(employee);
        });
    });

    // Seřadíme oddělení podle názvu
    const sortedDepartments = Object.keys(departments).sort();

    // Vygenerujeme HTML
    departmentsList.innerHTML = '';

    // Přidáme "Všichni zaměstnanci"
    const allItem = document.createElement('div');
    allItem.className = 'department-item active';
    allItem.setAttribute('data-department', 'all');
    allItem.innerHTML = `
        <div class="department-name">Všichni zaměstnanci</div>
        <div class="department-count">${employees.length} zaměstnanců</div>
    `;
    allItem.addEventListener('click', () => selectDirectoryDepartment('all', allItem));
    departmentsList.appendChild(allItem);

    // Přidáme jednotlivá oddělení
    sortedDepartments.forEach(dept => {
        const item = document.createElement('div');
        item.className = 'department-item';
        item.setAttribute('data-department', dept);
        item.innerHTML = `
            <div class="department-name">${dept}</div>
            <div class="department-count">${departments[dept].length} zaměstnanců</div>
        `;
        item.addEventListener('click', () => selectDirectoryDepartment(dept, item));
        departmentsList.appendChild(item);
    });
}

// Funkce pro výběr oddělení v directory
function selectDirectoryDepartment(department, element) {
    // Odstraníme active třídu ze všech položek
    document.querySelectorAll('#departmentsListDirectory .department-item').forEach(item => {
        item.classList.remove('active');
    });

    // Přidáme active třídu k vybrané položce
    element.classList.add('active');

    // Aktualizujeme název oddělení v unified search component
    const directoryCurrentSection = document.getElementById('directoryCurrentSection');
    if (directoryCurrentSection) {
        directoryCurrentSection.textContent = department === 'all' ? 'Všichni zaměstnanci' : department;
    }

    // Filtrujeme zaměstnance podle oddělení
    if (department === 'all') {
        filteredEmployees = [...employees];
        // Pro všechny zaměstnance používáme abecední řazení podle příjmení
        filteredEmployees = sortEmployeesAlphabetically(filteredEmployees);
    } else {
        filteredEmployees = employees.filter(employee => {
            return employee.oddeleni.split(',').map(d => d.trim()).includes(department);
        });
        // Pro konkrétní oddělení používáme hierarchické řazení (vedoucí první)
        filteredEmployees = sortEmployeesHierarchically(filteredEmployees);
    }

    // Aktualizujeme zobrazení
    generateEmployeeHTML();

    // Aktualizujeme štítky zaměstnanců (důležité pro Lobotková)
    updateEmployeeBadges();
    updateEmployeeNumbers();
}

// Funkce pro aktualizaci štítků zaměstnanců (zejména pro Lobotková)
function updateEmployeeBadges() {
    const employeeCards = document.querySelectorAll('.employee');

    employeeCards.forEach(card => {
        const nameElement = card.querySelector('.first-name, .last-name');
        if (!nameElement) return;

        // Získáme jméno zaměstnance z karty
        const firstName = card.querySelector('.first-name')?.textContent || '';
        const lastName = card.querySelector('.last-name')?.textContent || '';
        const fullName = `${firstName} ${lastName}`.trim();

        // Speciální logika pro Lobotková
        if (fullName === 'Lobotková Alena') {
            const currentDepartment = getCurrentSelectedDepartment();
            const existingBadge = card.querySelector('.hierarchy-badge');

            if (existingBadge) {
                if (currentDepartment === 'Představenstvo') {
                    existingBadge.className = 'hierarchy-badge predstavenstvo';
                    existingBadge.innerHTML = '<i class="fas fa-crown"></i> Představenstvo';
                } else if (currentDepartment === 'Účetnictví a daně') {
                    existingBadge.className = 'hierarchy-badge vedouci';
                    existingBadge.innerHTML = '<i class="fas fa-user-tie"></i> Vedoucí';
                } else {
                    // Výchozí štítek
                    existingBadge.className = 'hierarchy-badge predstavenstvo';
                    existingBadge.innerHTML = '<i class="fas fa-crown"></i> Představenstvo';
                }
            }
        }
    });
}

// Načítání dat ze souboru zamestnanci.json
async function loadEmployeeData() {
    try {
        const response = await fetch('zamestnanci.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        console.log('Data úspěšně načtena ze zamestnanci.json:', data.length, 'zaměstnanců');
        return data;
    } catch (error) {
        console.log('Používám lokální data (CORS nebo jiná chyba):', error.message);
        // Tiše použijeme fallback data
        return getHardcodedEmployeesData();
    }
}

// Fallback hardcoded data s kompletními pozicemi markerů (ze souboru mapa.html)
function getHardcodedEmployeesData() {
    return [
        {
            "jmeno": "Beáta Barošová",
            "pozice": "Specialista",
            "oddeleni": "Smluvní vztahy a povolenky",
            "obrazek": "img/Barosova.png",
            "telefon": "234 686 370",
            "mobil": "603 568 443",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1560,
            "top": 169
        },
        {
            "jmeno": "Bednář Petr",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/Bednar.png",
            "telefon": "234 686 340",
            "mobil": "734 173 784",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1200,
            "top": 131
        },
        {
            "jmeno": "Bílek Milan",
            "pozice": "Specialista",
            "oddeleni": "Bilance plynu",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 387",
            "mobil": "602 313 123",
            "email": "<EMAIL>",
            "teams": "",
            "left": 855,
            "top": 187
        },
        {
            "jmeno": "Bočánek Stanislav",
            "pozice": "Specialista",
            "oddeleni": "Bilance plynu",
            "obrazek": "img/Bocanek.jpeg",
            "telefon": "234 686 212",
            "mobil": "603 581 503",
            "email": "<EMAIL>",
            "teams": "",
            "left": 894,
            "top": 130
        },
        {
            "jmeno": "Boháč Kryštof",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/Bohac.jpeg",
            "telefon": "234 686 344",
            "mobil": "603 585 405",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1148,
            "top": 173
        },
        {
            "jmeno": "Bok Zbyněk",
            "pozice": "Specialista",
            "oddeleni": "Energetické trhy",
            "obrazek": "img/Bok.png",
            "telefon": "234 686 160",
            "mobil": "605 296 770",
            "email": "<EMAIL>",
            "teams": "",
            "left": 647,
            "top": 100
        },
        {
            "jmeno": "Brzobohatá Jana",
            "pozice": "Specialista",
            "oddeleni": "Bilance elektřiny",
            "obrazek": "img/Brzobohata.png",
            "telefon": "234 686 260",
            "mobil": "733 716 793",
            "email": "<EMAIL>",
            "teams": "",
            "left": 697,
            "top": 180
        },
        {
            "jmeno": "Česáková Andrea",
            "pozice": "Specialista",
            "oddeleni": "Smluvní vztahy a povolenky",
            "obrazek": "img/Cesakova.jpeg",
            "telefon": "234 686 375",
            "mobil": "605 296 856",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1640,
            "top": 168
        },
        {
            "jmeno": "Čermák Martin",
            "pozice": "Specialista",
            "oddeleni": "Financování a controlling",
            "obrazek": "img/Cermak.png",
            "telefon": "234 686 409",
            "mobil": "604 557 119",
            "email": "<EMAIL>",
            "teams": "",
            "left": 721,
            "top": 612
        },
        {
            "jmeno": "Drdák Josef",
            "pozice": "Specialista",
            "oddeleni": "Veřejné zakázky a právní servis",
            "obrazek": "img/Drdak.jfif",
            "telefon": "234 686 391",
            "mobil": "604 478 911",
            "email": "<EMAIL>",
            "teams": "",
            "left": 72,
            "top": 304
        },
        {
            "jmeno": "Dvořák Tomáš",
            "pozice": "Specialista",
            "oddeleni": "Bilance plynu",
            "obrazek": "img/Dvorak.jpeg",
            "telefon": "234 686 211",
            "mobil": "605 296 775",
            "email": "<EMAIL>",
            "teams": "",
            "left": 855,
            "top": 221
        },
        {
            "jmeno": "Váchalová Zuzana",
            "pozice": "Vedoucí",
            "oddeleni": "Kancelář PAS",
            "obrazek": "img/Dvorakova.jpeg",
            "telefon": "234 686 380",
            "mobil": "724 585 599",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1652,
            "top": 578
        },
        {
            "jmeno": "Erhartová Pavla",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/Erhartova.jpeg",
            "telefon": "234 686 336",
            "mobil": "731 502 321",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1148,
            "top": 134
        },
        {
            "jmeno": "Fridrichová Katarína",
            "pozice": "Specialista",
            "oddeleni": "Správa majetku a služby",
            "obrazek": "img/Fridrichova.png",
            "telefon": "",
            "mobil": "",
            "email": "",
            "teams": "",
            "left": 1212,
            "top": 578
        },
        {
            "jmeno": "Gabriel Martina",
            "pozice": "Vedoucí",
            "oddeleni": "Záruky původu a čistá mobilita",
            "obrazek": "img/Gabriel.png",
            "telefon": "234 686 283",
            "mobil": "731 446 022",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1455,
            "top": 151
        },
        {
            "jmeno": "Gregor Boris",
            "pozice": "Specialista",
            "oddeleni": "Bilance plynu",
            "obrazek": "img/Gregor.png",
            "telefon": "234 686 213",
            "mobil": "730 811 602",
            "email": "<EMAIL>",
            "teams": "",
            "left": 894,
            "top": 199
        },
        {
            "jmeno": "Haufenhoferová Eva",
            "pozice": "Specialista",
            "oddeleni": "Bilance elektřiny",
            "obrazek": "img/Haufenhoferova.png",
            "telefon": "234 686 265",
            "mobil": "739 338 543",
            "email": "<EMAIL>",
            "teams": "",
            "left": 697,
            "top": 213
        },
        {
            "jmeno": "Hesko Martin",
            "pozice": "Specialista",
            "oddeleni": "Financování a controlling",
            "obrazek": "img/Hesko.png",
            "telefon": "234 686 401",
            "mobil": "603 165 089",
            "email": "<EMAIL>",
            "teams": "",
            "left": 791,
            "top": 573
        },
        {
            "jmeno": "Hodánek Jaroslav",
            "pozice": "Vedoucí",
            "oddeleni": "Bilance elektřiny",
            "obrazek": "img/Hodanek.png",
            "telefon": "234 686 255",
            "mobil": "605 296 771",
            "email": "<EMAIL>",
            "teams": "",
            "left": 804,
            "top": 116
        },
        {
            "jmeno": "Hons Jindřich",
            "pozice": "Specialista",
            "oddeleni": "Smluvní vztahy a povolenky",
            "obrazek": "img/Hons.png",
            "telefon": "234 686 372",
            "mobil": "730 811 497",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1607,
            "top": 170
        },
        {
            "jmeno": "Horová Žaneta",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 311",
            "mobil": "603 970 415",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1075,
            "top": 164
        },
        {
            "jmeno": "Houdek Ondřej",
            "pozice": "Specialista",
            "oddeleni": "Financování a controlling",
            "obrazek": "img/Houdek.png",
            "telefon": "234 686 403",
            "mobil": "730 182 831",
            "email": "<EMAIL>",
            "teams": "",
            "left": 791,
            "top": 606
        },
        {
            "jmeno": "Hrdá Veronika",
            "pozice": "Specialista",
            "oddeleni": "Smluvní vztahy a povolenky",
            "obrazek": "img/Hrda.jpeg",
            "telefon": "234 686 361",
            "mobil": "739 691 290",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1691,
            "top": 170
        },
        {
            "jmeno": "Hůlová Helena",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/Hulova.jpg",
            "telefon": "234 686 343",
            "mobil": "603 341 439",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1120,
            "top": 166
        },
        {
            "jmeno": "Chemišinec Igor",
            "pozice": "Místopředseda představenstva",
            "oddeleni": "Představenstvo",
            "obrazek": "img/Chemisinec.jpeg",
            "telefon": "234 686 122",
            "mobil": "731 502 327",
            "email": "<EMAIL>",
            "teams": "",
            "left": 400,
            "top": 612
        },
        {
            "jmeno": "Jedličková Markéta",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/Jedlickova1.png",
            "telefon": "234 686 317",
            "mobil": "730 556 460",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1075,
            "top": 126
        },
        {
            "jmeno": "Jahoda Vojtěch",
            "pozice": "Vedoucí",
            "oddeleni": "POZE",
            "obrazek": "img/Jahoda.png",
            "telefon": "234 686 305",
            "mobil": "731 124 500",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1268,
            "top": 144
        },
        {
            "jmeno": "Jindrová Jiřina",
            "pozice": "Specialista",
            "oddeleni": "Účetnictví a daně",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 407",
            "mobil": "734 238 886",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1752,
            "top": 572
        },
        {
            "jmeno": "Jindrová Petra",
            "pozice": "Specialista",
            "oddeleni": "Záruky původu a čistá mobilita",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 365",
            "mobil": "605 597 237",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1320,
            "top": 146
        },
        {
            "jmeno": "Kalábová Lucie",
            "pozice": "Specialista",
            "oddeleni": "Bilance elektřiny",
            "obrazek": "img/Kalabova.jpeg",
            "telefon": "234 686 264",
            "mobil": "604 130 425",
            "email": "<EMAIL>",
            "teams": "",
            "left": 730,
            "top": 107
        },
        {
            "jmeno": "Kánský Jiří",
            "pozice": "Specialista",
            "oddeleni": "Rozvoj trhu",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 284",
            "mobil": "797 897 509",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1867,
            "top": 303
        },
        {
            "jmeno": "Karas Karel",
            "pozice": "Specialista",
            "oddeleni": "Energetické trhy",
            "obrazek": "img/Karas.jpg",
            "telefon": "234 686 164",
            "mobil": "730 811 500",
            "email": "<EMAIL>",
            "teams": "",
            "left": 575,
            "top": 118
        },
        {
            "jmeno": "Kníže Jaromír",
            "pozice": "Specialista",
            "oddeleni": "Bilance plynu",
            "obrazek": "img/Knize.jpeg",
            "telefon": "243 686 216",
            "mobil": "732 478 760",
            "email": "<EMAIL>",
            "teams": "",
            "left": 855,
            "top": 118
        },
        {
            "jmeno": "Knop Ondřej",
            "pozice": "Specialista",
            "oddeleni": "Bilance plynu",
            "obrazek": "img/Knop.png",
            "telefon": "234 686 217",
            "mobil": "731 617 280",
            "email": "<EMAIL>",
            "teams": "",
            "left": 894,
            "top": 157
        },
        {
            "jmeno": "Kohoutová Kateřina",
            "pozice": "Specialista",
            "oddeleni": "Energetické trhy",
            "obrazek": "img/Kohoutova.jpeg",
            "telefon": "234 686 168",
            "mobil": "737 707 104",
            "email": "<EMAIL>",
            "teams": "",
            "left": 595,
            "top": 140
        },
        {
            "jmeno": "Kopecká Zuzana",
            "pozice": "Specialista",
            "oddeleni": "Účetnictví a daně",
            "obrazek": "img/Kopecka.png",
            "telefon": "234 686 405",
            "mobil": "737 284 190",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1820,
            "top": 580
        },
        {
            "jmeno": "Kreuzman Jiří",
            "pozice": "Vedoucí",
            "oddeleni": "Veřejné zakázky a právní servis",
            "obrazek": "img/Kreuzman.png",
            "telefon": "234 686 384",
            "mobil": "603 585 492",
            "email": "<EMAIL>",
            "teams": "",
            "left": 98,
            "top": 212
        },
        {
            "jmeno": "Křivánek Libor",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/Krivanek.jpg",
            "telefon": "234 686 341",
            "mobil": "731 626 996",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1199,
            "top": 173
        },
        {
            "jmeno": "Kurfiřtová Pavla",
            "pozice": "Recepční",
            "oddeleni": "Kancelář PAS",
            "obrazek": "img/Kurfirtova.jpeg",
            "telefon": "",
            "mobil": "",
            "email": "",
            "teams": "",
            "left": 1627,
            "top": 426
        },
        {
            "jmeno": "Laco Dušan",
            "pozice": "Specialista",
            "oddeleni": "Bilance plynu",
            "obrazek": "img/Laco.jpeg",
            "telefon": "234 686 388",
            "mobil": "734 354 023",
            "email": "<EMAIL>",
            "teams": "",
            "left": 359,
            "top": 99
        },
        {
            "jmeno": "Láník Libor",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 312",
            "mobil": "604 352 702",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1015,
            "top": 205
        },
        {
            "jmeno": "Lebeda Dušan",
            "pozice": "Specialista",
            "oddeleni": "Financování a controlling",
            "obrazek": "img/Lebeda.jpg",
            "telefon": "234 686 402",
            "mobil": "603 217 358",
            "email": "<EMAIL>",
            "teams": "",
            "left": 721,
            "top": 577
        },
        {
            "jmeno": "Lobotková Alena",
            "pozice": "Vedoucí, členka představenstva",
            "oddeleni": "Účetnictví a daně, Představenstvo",
            "obrazek": "img/Lobotkova.png",
            "telefon": "234 686 415",
            "mobil": "734 253 127",
            "email": "<EMAIL>",
            "teams": "",
            "left": 305,
            "top": 557
        },
        {
            "jmeno": "Máca Ondřej",
            "pozice": "Vedoucí",
            "oddeleni": "Rozvoj trhu",
            "obrazek": "img/Maca.png",
            "telefon": "234 686 280",
            "mobil": "603 165 097",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1735,
            "top": 216
        },
        {
            "jmeno": "Mácová Michaela",
            "pozice": "Specialista",
            "oddeleni": "Záruky původu a čistá mobilita",
            "obrazek": "img/Macova.jpeg",
            "telefon": "234 686 363",
            "mobil": "731 436 830",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1380,
            "top": 183
        },
        {
            "jmeno": "Mareš Jan",
            "pozice": "Specialista",
            "oddeleni": "Energetické trhy",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 161",
            "mobil": "603 785 790",
            "email": "<EMAIL>",
            "teams": "",
            "left": 647,
            "top": 135
        },
        {
            "jmeno": "Mašková Hana",
            "pozice": "Specialista",
            "oddeleni": "Bilance elektřiny",
            "obrazek": "img/Maskova.png",
            "telefon": "234 686 266",
            "mobil": "734 771 350",
            "email": "<EMAIL>",
            "teams": "",
            "left": 730,
            "top": 141
        },
        {
            "jmeno": "Nardelli Magdalena",
            "pozice": "Specialista",
            "oddeleni": "Účetnictví a daně",
            "obrazek": "img/Maresova.jpeg",
            "telefon": "234 686 410",
            "mobil": "603 345 342",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1554,
            "top": 578
        },
        {
            "jmeno": "Mňuková Kateřina",
            "pozice": "Specialista",
            "oddeleni": "Účetnictví a daně",
            "obrazek": "img/Mnukova.png",
            "telefon": "234 686 404",
            "mobil": "730 585 502",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1871,
            "top": 578
        },
        {
            "jmeno": "Mesteková Alice",
            "pozice": "Specialista",
            "oddeleni": "Záruky původu a čistá mobilita",
            "obrazek": "img/Mestekova.jpg",
            "telefon": "234 686 364",
            "mobil": "733 662 289",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1380,
            "top": 148
        },
        {
            "jmeno": "Mičáň Alex",
            "pozice": "Specialista",
            "oddeleni": "Compliance",
            "obrazek": "img/Mican.png",
            "telefon": "234 686 386",
            "mobil": "734 654 695",
            "email": "<EMAIL>",
            "teams": "",
            "left": 891,
            "top": 579
        },
        {
            "jmeno": "Nečesaný Jakub",
            "pozice": "Vedoucí",
            "oddeleni": "Bilance plynu",
            "obrazek": "img/Necesany.jpeg",
            "telefon": "234 686 205",
            "mobil": "731 502 326",
            "email": "<EMAIL>",
            "teams": "",
            "left": 978,
            "top": 121
        },
        {
            "jmeno": "Nohejlová Jiřina",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 337",
            "mobil": "731 433 683",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1120,
            "top": 131
        },
        {
            "jmeno": "Novák Petr",
            "pozice": "Vedoucí",
            "oddeleni": "Compliance",
            "obrazek": "img/Novak.png",
            "telefon": "234 686 390",
            "mobil": "730 139 001",
            "email": "<EMAIL>",
            "teams": "",
            "left": 440,
            "top": 572
        },
        {
            "jmeno": "Pecánek Tomáš",
            "pozice": "Specialista",
            "oddeleni": "Bilance plynu",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 214",
            "mobil": "604 182 733",
            "email": "<EMAIL>",
            "teams": "",
            "left": 855,
            "top": 151
        },
        {
            "jmeno": "Pešková Monika",
            "pozice": "Specialista",
            "oddeleni": "Rozvoj trhu",
            "obrazek": "img/Peskova.jpeg",
            "telefon": "234 686 289",
            "mobil": "603 436 951",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1814,
            "top": 215
        },
        {
            "jmeno": "Prihara Roman",
            "pozice": "Specialista",
            "oddeleni": "Bilance elektřiny",
            "obrazek": "img/Prihara.png",
            "telefon": "234 686 261",
            "mobil": "605 296 774",
            "email": "<EMAIL>",
            "teams": "",
            "left": 730,
            "top": 180
        },
        {
            "jmeno": "Procházková Kateřina",
            "pozice": "Recepční",
            "oddeleni": "Kancelář PAS",
            "obrazek": "img/Prochazkova.jpeg",
            "telefon": "234 686 113",
            "mobil": "734 467 240",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1627,
            "top": 374
        },
        {
            "jmeno": "Puchel Michal",
            "pozice": "Předseda představenstva",
            "oddeleni": "Představenstvo",
            "obrazek": "img/Puchel.jpg",
            "telefon": "234 686 123",
            "mobil": "608 528 850",
            "email": "<EMAIL>",
            "teams": "",
            "left": 148,
            "top": 484
        },
        {
            "jmeno": "Raška Michal",
            "pozice": "Vedoucí",
            "oddeleni": "Správa majetku a služby",
            "obrazek": "img/Raska.png",
            "telefon": "234 686 435",
            "mobil": "731 413 587",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1320,
            "top": 607
        },
        {
            "jmeno": "Rýdl Zdeněk",
            "pozice": "Specialista",
            "oddeleni": "Energetické trhy",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 166",
            "mobil": "604 519 875",
            "email": "<EMAIL>",
            "teams": "",
            "left": 522,
            "top": 121
        },
        {
            "jmeno": "Ryšavý Miroslav",
            "pozice": "Specialista",
            "oddeleni": "Energetické trhy",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 165",
            "mobil": "730 811 528",
            "email": "<EMAIL>",
            "teams": "",
            "left": 595,
            "top": 104
        },
        {
            "jmeno": "Sharashenidze Akaki",
            "pozice": "Specialista",
            "oddeleni": "Energetické trhy",
            "obrazek": "img/Akaki.jpeg",
            "telefon": "234 686 163",
            "mobil": "605 543 706",
            "email": "<EMAIL>",
            "teams": "",
            "left": 647,
            "top": 173
        },
        {
            "jmeno": "Smrček Petr",
            "pozice": "Vedoucí",
            "oddeleni": "Kybernetická bezpečnost a ochrana dat",
            "obrazek": "img/Smrcek.png",
            "telefon": "234 686 430",
            "mobil": "605 296 773",
            "email": "<EMAIL>",
            "teams": "",
            "left": 954,
            "top": 610
        },
        {
            "jmeno": "Sojka Alena",
            "pozice": "Specialista",
            "oddeleni": "Správa majetku a služby",
            "obrazek": "img/Sojka.png",
            "telefon": "234 686 439",
            "mobil": "734 771 138",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1271,
            "top": 608
        },
        {
            "jmeno": "Soukupová Michaela",
            "pozice": "Specialista",
            "oddeleni": "Kancelář PAS",
            "obrazek": "img/Soukupovapng.png",
            "telefon": "234 686 411",
            "mobil": "733 730 050",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1605,
            "top": 578
        },
        {
            "jmeno": "Srb Václav",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/Srb.jpg",
            "telefon": "234 686 313",
            "mobil": "731 143 930",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1015,
            "top": 124
        },
        {
            "jmeno": "Staňková Michaela",
            "pozice": "Specialista",
            "oddeleni": "Účetnictví a daně",
            "obrazek": "img/Stankova.png",
            "telefon": "234 686 408",
            "mobil": "604 194 635",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1871,
            "top": 542
        },
        {
            "jmeno": "Stašková Zuzana",
            "pozice": "Vedoucí",
            "oddeleni": "Smluvní vztahy a povolenky",
            "obrazek": "img/Staskova.png",
            "telefon": "234 686 371",
            "mobil": "734 353 950",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1488,
            "top": 154
        },
        {
            "jmeno": "Šmídek Filip",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 314",
            "mobil": "731 095 865",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1015,
            "top": 163
        },
        {
            "jmeno": "Šrom Jakub",
            "pozice": "Vedoucí",
            "oddeleni": "Energetické trhy",
            "obrazek": "img/Srom.png",
            "telefon": "234 686 155",
            "mobil": "739 505 972",
            "email": "<EMAIL>",
            "teams": "",
            "left": 461,
            "top": 114
        },
        {
            "jmeno": "Špala Jaroslav",
            "pozice": "Specialista",
            "oddeleni": "Energetické trhy",
            "obrazek": "img/Spala.jpeg",
            "telefon": "234 686 181",
            "mobil": "703 472 005",
            "email": "<EMAIL>",
            "teams": "",
            "left": 575,
            "top": 158
        },
        {
            "jmeno": "Tepličanec Pavel",
            "pozice": "Specialista",
            "oddeleni": "Správa majetku a služby",
            "obrazek": "img/Teplicanec.png",
            "telefon": "234 686 437",
            "mobil": "730 139 018",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1212,
            "top": 612
        },
        {
            "jmeno": "Tomek Jiří",
            "pozice": "Specialista",
            "oddeleni": "Bilance elektřiny",
            "obrazek": "img/Tomek.png",
            "telefon": "234 686 262",
            "mobil": "605 296 776",
            "email": "<EMAIL>",
            "teams": "",
            "left": 697,
            "top": 108
        },
        {
            "jmeno": "Tůma Tomáš",
            "pozice": "Specialista",
            "oddeleni": "ICT služby",
            "obrazek": "img/Tuma.png",
            "telefon": "234 686 431",
            "mobil": "703 690 250",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1140,
            "top": 616
        },
        {
            "jmeno": "Vacek Jaroslav",
            "pozice": "Specialista",
            "oddeleni": "POZE",
            "obrazek": "img/Vacek.png",
            "telefon": "234 686 342",
            "mobil": "730 811 554",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1120,
            "top": 201
        },
        {
            "jmeno": "Valent Lajos",
            "pozice": "Specialista",
            "oddeleni": "Rozvoj trhu",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 287",
            "mobil": "731 395 991",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1867,
            "top": 360
        },
        {
            "jmeno": "Vasjuňkina Varvara",
            "pozice": "Specialista",
            "oddeleni": "Rozvoj trhu",
            "obrazek": "img/Varvara.jpeg",
            "telefon": "",
            "mobil": "",
            "email": "",
            "teams": "",
            "left": 1841,
            "top": 264
        },
        {
            "jmeno": "Vích Ondřej",
            "pozice": "Vedoucí",
            "oddeleni": "ICT služby",
            "obrazek": "img/Vich.jpeg",
            "telefon": "234 686 420",
            "mobil": "734 565 946",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1029,
            "top": 614
        },
        {
            "jmeno": "Vichrová Martina",
            "pozice": "Specialista",
            "oddeleni": "Veřejné zakázky a právní servis",
            "obrazek": "img/Vichrova.jpeg",
            "telefon": "234 686 383",
            "mobil": "603 928 286",
            "email": "<EMAIL>",
            "teams": "",
            "left": 75,
            "top": 358
        },
        {
            "jmeno": "Vlčková Soňa",
            "pozice": "Specialista",
            "oddeleni": "Smluvní vztahy a povolenky",
            "obrazek": "img/Vlckova.jpeg",
            "telefon": "234 686 376",
            "mobil": "704 951 727",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1691,
            "top": 211
        },
        {
            "jmeno": "Záviský Ondřej",
            "pozice": "Vedoucí",
            "oddeleni": "Financování a controlling",
            "obrazek": "img/Zavisky.jpg",
            "telefon": "234 686 400",
            "mobil": "731 412 963",
            "email": "<EMAIL>",
            "teams": "",
            "left": 664,
            "top": 609
        },
        {
            "jmeno": "Zelenková Jana",
            "pozice": "Specialista",
            "oddeleni": "Účetnictví a daně",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 406",
            "mobil": "730 811 582",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1820,
            "top": 544
        },
        {
            "jmeno": "Zezuláková Andrea",
            "pozice": "Specialista",
            "oddeleni": "Bilance elektřiny",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 263",
            "mobil": "730 111 471",
            "email": "<EMAIL>",
            "teams": "",
            "left": 697,
            "top": 143
        },
        {
            "jmeno": "Sylvie Karolová",
            "pozice": "Specialista",
            "oddeleni": "Rozvoj trhu",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 286",
            "mobil": "799 018 407",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1823,
            "top": 333
        },
        {
            "jmeno": "Radka Maňurová",
            "pozice": "Specialista",
            "oddeleni": "Rozvoj trhu",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 281",
            "mobil": "776 123 745",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1871,
            "top": 226
        },
        {
            "jmeno": "František Černý",
            "pozice": "Specialista",
            "oddeleni": "ICT služby",
            "obrazek": "img/no-person-photo.png",
            "telefon": "234 686 432",
            "mobil": "731 523 752",
            "email": "<EMAIL>",
            "teams": "",
            "left": 1085,
            "top": 616
        }
    ];
}


function generateDepartmentFilters() {
    const departmentFiltersContainer = document.getElementById('department-filters');
    if (!departmentFiltersContainer) {
        console.error('Element #department-filters nenalezen');
        return;
    }


    const allDepartments = [];
    employees.forEach(emp => {
        const depts = emp.oddeleni.split(',').map(d => d.trim());
        allDepartments.push(...depts);
    });
    const departments = [...new Set(allDepartments)].sort();


    departmentFiltersContainer.innerHTML = '';


    const allButton = document.createElement('button');
    allButton.className = 'filter-btn all active';
    allButton.setAttribute('data-filter', 'all');
    allButton.innerHTML = `<i class="fas fa-users"></i> Všichni zaměstnanci <span class="count">(${employees.length})</span>`;
    departmentFiltersContainer.appendChild(allButton);



  
    departments.forEach(department => {
        const button = document.createElement('button');
        button.className = 'filter-btn';
        button.setAttribute('data-filter', department);


        const count = employees.filter(emp => {
            const depts = emp.oddeleni.split(',').map(d => d.trim());
            return depts.includes(department);
        }).length;

        
        let icon = 'fas fa-building';
        if (department.includes('elektřin')) icon = 'fas fa-bolt';
        else if (department.includes('plyn')) icon = 'fas fa-fire';
        else if (department.includes('POZE')) icon = 'fas fa-leaf';
        else if (department.includes('trhy')) icon = 'fas fa-chart-line';
        else if (department.includes('ICT')) icon = 'fas fa-laptop-code';
        else if (department.includes('Financování')) icon = 'fas fa-calculator';
        else if (department.includes('PAS')) icon = 'fas fa-briefcase';
        else if (department.includes('Záruky')) icon = 'fas fa-certificate';
        else if (department.includes('Veřejné')) icon = 'fas fa-gavel';
        else if (department.includes('Účetnictví')) icon = 'fas fa-book';
        else if (department.includes('Smluvní')) icon = 'fas fa-file-contract';
        else if (department.includes('Správa')) icon = 'fas fa-building';
        else if (department.includes('Kybernetická')) icon = 'fas fa-shield-alt';
        else if (department.includes('Rozvoj')) icon = 'fas fa-chart-bar';
        else if (department.includes('Compliance')) icon = 'fas fa-balance-scale';
        else if (department.includes('Představenstvo')) icon = 'fas fa-users';

        button.innerHTML = `<i class="${icon}"></i> ${department} <span class="count">(${count})</span>`;
        departmentFiltersContainer.appendChild(button);
    });

   
    setupFilterEventListeners();
}


function generateEmployeeHTML() {
    const employeeGrid = document.getElementById('employee-grid');
    if (!employeeGrid) {
        console.error('Element #employee-grid nenalezen');
        return;
    }

   
    employeeGrid.innerHTML = '<div class="loading-container"><div class="loading-spinner"></div><p>Načítání zaměstnanců...</p></div>';

   
    setTimeout(() => {
        employeeGrid.innerHTML = '';

        if (filteredEmployees.length === 0) {
            employeeGrid.innerHTML = '<div class="no-results"><i class="fas fa-search"></i><p>Žádní zaměstnanci k zobrazení</p><p class="hint">Zkuste změnit filtr nebo vyhledávací dotaz</p></div>';
            return;
        }

        renderEmployees();
    }, 200);
}

function renderEmployees() {
    const employeeGrid = document.getElementById('employee-grid');


    filteredEmployees.forEach((employee, index) => {
        const employeeDiv = document.createElement('div');

        // Detekce typu pozice pro badge
        const poziceLower = employee.pozice.toLowerCase();
        const isPredseda = poziceLower.includes('předseda') || poziceLower.includes('místopředseda');
        const isVedouci = poziceLower.includes('vedoucí') || poziceLower.includes('ředitel');
        const isManager = isPredseda || isVedouci;

        // Všichni zaměstnanci mají stejnou CSS třídu - bez hierarchického řazení
        employeeDiv.className = 'employee';
        employeeDiv.setAttribute('data-departments', employee.oddeleni);
        employeeDiv.setAttribute('data-number', index + 1);

        const nameParts = employee.jmeno.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';

        // Určíme typ štítku a ikonu - vylepšená logika pro Lobotková
        let hierarchyBadge = '';

        // Speciální logika pro Lobotková - štítek závisí na aktuálně vybraném oddělení
        if (employee.jmeno === 'Lobotková Alena') {
            const currentDepartment = getCurrentSelectedDepartment();
            if (currentDepartment === 'Představenstvo') {
                hierarchyBadge = '<div class="hierarchy-badge predstavenstvo"><i class="fas fa-crown"></i> Představenstvo</div>';
            } else if (currentDepartment === 'Účetnictví a daně') {
                hierarchyBadge = '<div class="hierarchy-badge vedouci"><i class="fas fa-user-tie"></i> Vedoucí</div>';
            } else {
                // Výchozí štítek pokud není vybrané specifické oddělení
                hierarchyBadge = '<div class="hierarchy-badge predstavenstvo"><i class="fas fa-crown"></i> Představenstvo</div>';
            }
        } else {
            // Standardní logika pro ostatní zaměstnance
            if (isPredseda) {
                hierarchyBadge = '<div class="hierarchy-badge predstavenstvo"><i class="fas fa-crown"></i> Představenstvo</div>';
            } else if (isVedouci) {
                hierarchyBadge = '<div class="hierarchy-badge vedouci"><i class="fas fa-user-tie"></i> Vedoucí</div>';
            }
        }

        employeeDiv.innerHTML = `
            ${hierarchyBadge}
            <img src="${employee.obrazek}" alt="${employee.jmeno}" onerror="this.src='img/no-person-photo.png'">
            <div class="employee_name">
                <div class="first-name">${firstName}</div>
                <div class="last-name">${lastName}</div>
            </div>
            <p class="position">${employee.pozice}</p>
            <p class="job_type">${employee.oddeleni}</p>

            <div class="click-info">
                <i class="fas fa-hand-pointer"></i>
                <span>Více informací zobrazíte kliknutím</span>
            </div>
        `;


        // Přidáme event listener s lepším error handlingem
        employeeDiv.addEventListener('click', (event) => {
            console.log('Employee card clicked:', employee.jmeno);
            event.preventDefault();
            event.stopPropagation();

            // Kontrola existence modálního okna před otevřením
            const modal = document.getElementById('employeeModal');
            if (!modal) {
                console.error('Modal element not found!');
                return;
            }

            try {
                openModal(employee);
            } catch (error) {
                console.error('Error opening modal:', error);
            }
        });

        employeeGrid.appendChild(employeeDiv);

        
        setTimeout(() => {
            employeeDiv.style.opacity = '0';
            employeeDiv.style.transform = 'translateY(20px)';
            employeeDiv.style.transition = 'all 0.4s ease';

            setTimeout(() => {
                employeeDiv.style.opacity = '1';
                employeeDiv.style.transform = 'translateY(0)';
            }, index * 50);
        }, 0);
    });

    
    addSpinEffect();
}




function setupFilterEventListeners() {
    const filterButtons = document.querySelectorAll('.filter-btn');

    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            
            filterButtons.forEach(btn => btn.classList.remove('active'));

           
            button.classList.add('active');

            
            const filter = button.getAttribute('data-filter');

            
            if (filter === 'all') {
                filteredEmployees = [...employees];

            } else {
                filteredEmployees = employees.filter(emp => {
                    const depts = emp.oddeleni.split(',').map(d => d.trim());
                    return depts.includes(filter);
                });
            }


            // Abecední seřazení podle příjmení
            sortEmployeesAlphabetically(filteredEmployees);

            generateEmployeeHTML();
            updateEmployeeNumbers();
        });
    });
}




// Funkce pro získání aktuálně vybraného oddělení
function getCurrentSelectedDepartment() {
    // Zkusíme najít aktivní oddělení v directory sekci
    let activeItem = document.querySelector('#departmentsListDirectory .department-item.active');

    // Pokud není v directory, zkusíme mapu
    if (!activeItem) {
        activeItem = document.querySelector('#departmentsList .department-item.active');
    }

    if (activeItem) {
        // Získáme název oddělení z data-department atributu
        const departmentName = activeItem.getAttribute('data-department');
        if (departmentName === 'all') {
            return 'Všichni zaměstnanci';
        }
        return departmentName;
    }
    return null;
}

function openModal(employeeData) {
    console.log('openModal called with:', employeeData);

    // Kontrola vstupních dat
    if (!employeeData) {
        console.error('No employee data provided to openModal');
        return;
    }

    const modal = document.getElementById('employeeModal');
    const modalImage = document.getElementById('modalImage');
    const modalName = document.getElementById('modalName');
    const modalPosition = document.getElementById('modalPosition');
    const modalDepartment = document.getElementById('modalDepartment');
    const modalOffice = document.getElementById('modalOffice');
    const modalPhone = document.getElementById('modalPhone');
    const modalMobile = document.getElementById('modalMobile');
    const modalEmail = document.getElementById('modalEmail');
    const modalTeams = document.getElementById('modalTeams');

    // Kontrola existence všech potřebných elementů
    const requiredElements = [modal, modalImage, modalName, modalPosition, modalDepartment, modalOffice, modalPhone, modalMobile, modalEmail, modalTeams];
    const missingElements = requiredElements.filter(el => !el);

    if (missingElements.length > 0) {
        console.error('Missing modal elements:', missingElements.length);
        return;
    }

    
    modalImage.src = employeeData.obrazek;
    modalImage.alt = employeeData.jmeno;
    modalImage.onerror = function() {
        this.src = 'img/no-person-photo.png';
    };

   
    // Detekce typu pozice pro modální okno - vylepšená logika pro Lobotková
    const poziceLower = employeeData.pozice.toLowerCase();
    const currentDepartment = getCurrentSelectedDepartment(); // Získáme aktuálně vybrané oddělení

    const isPredseda = poziceLower.includes('předseda') || poziceLower.includes('místopředseda') ||
                      poziceLower.includes('členka představenstva') || poziceLower.includes('člen představenstva');
    const isVedouci = poziceLower.includes('vedoucí') || poziceLower.includes('ředitel');

    let badgeHtml = '';

    // Speciální logika pro Lobotková - štítek závisí na aktuálně vybraném oddělení
    if (employeeData.jmeno === 'Lobotková Alena') {
        if (currentDepartment === 'Představenstvo') {
            badgeHtml = '<span class="modal-badge predstavenstvo"><i class="fas fa-crown"></i> Představenstvo</span>';
        } else if (currentDepartment === 'Účetnictví a daně') {
            badgeHtml = '<span class="modal-badge vedouci"><i class="fas fa-user-tie"></i> Vedoucí</span>';
        } else {
            // Výchozí štítek pokud není vybrané specifické oddělení
            badgeHtml = '<span class="modal-badge predstavenstvo"><i class="fas fa-crown"></i> Představenstvo</span>';
        }
    } else {
        // Standardní logika pro ostatní zaměstnance
        if (isPredseda) {
            badgeHtml = '<span class="modal-badge predstavenstvo"><i class="fas fa-crown"></i> Představenstvo</span>';
        } else if (isVedouci) {
            badgeHtml = '<span class="modal-badge vedouci"><i class="fas fa-user-tie"></i> Vedoucí</span>';
        }
    }

    modalName.innerHTML = employeeData.jmeno + badgeHtml;

    // Aktualizace info karet s novým layoutem
    modalPosition.innerHTML = `<i class="fas fa-briefcase"></i><span>Pracovní pozice: ${employeeData.pozice}</span>`;
    modalDepartment.innerHTML = `<i class="fas fa-building"></i><span>Oddělení: ${employeeData.oddeleni}</span>`;


    // Vytvoříme tlačítko pro přepnutí na mapu a zvýraznění zaměstnance
    modalOffice.innerHTML = `
        <div class="modal-actions">
            <button onclick="showEmployeeOnMap('${employeeData.jmeno}')" class="modal-action-btn map-btn">
                <i class="fas fa-map-marker-alt"></i>
                Zobrazit na mapě
            </button>
        </div>
    `;



   
    modalPhone.textContent = employeeData.telefon ?
        `Telefon: ${formatPhoneNumber(employeeData.telefon)}` :
        'Telefon: Neuvedeno';
    modalMobile.textContent = employeeData.mobil ?
        `Mobil: ${formatPhoneNumber(employeeData.mobil)}` :
        'Mobil: Neuvedeno';
    modalEmail.innerHTML = employeeData.email ?
        `Email: <a href="mailto:${employeeData.email}" style="color: #00808f; text-decoration: none;">${employeeData.email}</a>` :
        'Email: Neuvedeno';

 
    if (employeeData.email) {
        const teamsUrl = `https://teams.microsoft.com/l/chat/0/0?users=${employeeData.email}`;
        modalTeams.innerHTML = `
            <div class="modal-actions">
                <a href="${teamsUrl}" target="_blank" class="modal-action-btn">
                    <i class="fab fa-microsoft"></i> Kontaktovat přes Teams
                </a>
                <a href="mailto:${employeeData.email}" class="modal-action-btn">
                    <i class="fas fa-envelope"></i> Poslat email
                </a>
            </div>
        `;
    } else {
        modalTeams.innerHTML = `
            <div class="modal-actions">
                <span style="color: #666; font-style: italic;">Kontaktní údaje nejsou k dispozici</span>
            </div>
        `;
    }

    modal.style.display = 'block';
}

// Zpřístupnění funkce globálně pro map-integration.js
window.openModal = openModal;


function getOfficeByDepartment(department) {
    const officeMap = {
        'Představenstvo': 'Praha - Karolinská 661/4, 4. patro',
        'Financování a controlling': 'Praha - Karolinská 661/4, 3. patro',
        'Účetnictví a daně': 'Praha - Karolinská 661/4, 3. patro',
        'ICT služby': 'Praha - Karolinská 661/4, 2. patro',
        'Kybernetická bezpečnost a ochrana dat': 'Praha - Karolinská 661/4, 2. patro',
        'Bilance elektřiny': 'Praha - Karolinská 661/4, 1. patro',
        'Bilance plynu': 'Praha - Karolinská 661/4, 1. patro',
        'POZE': 'Praha - Karolinská 661/4, 1. patro',
        'Energetické trhy': 'Praha - Karolinská 661/4, 1. patro',
        'Kancelář PAS': 'Praha - Karolinská 661/4, přízemí'
    };


    for (const [dept, office] of Object.entries(officeMap)) {
        if (department.includes(dept)) {
            return office;
        }
    }

    return 'Praha - Karolinská 661/4';
}

function generateSmartDescription(employee) {
    const position = employee.pozice.toLowerCase();
    const department = employee.oddeleni.toLowerCase();
    const firstName = employee.jmeno.split(' ')[0];

    if (position.includes('vedoucí')) {
        return `${firstName} vede tým oddělení ${employee.oddeleni} a zodpovídá za strategické řízení a koordinaci týmu.`;
    } else if (position.includes('předseda')) {
        return `${firstName} zastává pozici předsedy představenstva a řídí strategické směřování společnosti.`;
    } else if (position.includes('místopředseda')) {
        return `${firstName} podporuje předsedu představenstva v řízení společnosti a zastupuje ho v jeho nepřítomnosti.`;
    } else if (department.includes('ict')) {
        return `${firstName} se stará o IT infrastrukturu a technologické řešení ve společnosti.`;
    } else if (department.includes('účetnictví')) {
        return `${firstName} zajišťuje účetní agendu a finanční reporting společnosti.`;
    } else if (department.includes('bilance')) {
        return `${firstName} se specializuje na bilancování energií a zajišťuje přesnost energetických toků.`;
    } else {
        return `${firstName} pracuje jako ${employee.pozice} v oddělení ${employee.oddeleni} a přispívá k úspěchu týmu.`;
    }
}

function formatPhoneNumber(phone) {
    if (!phone) return null;
 
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 9) {
        return `+420 ${cleaned.substring(0,3)} ${cleaned.substring(3,6)} ${cleaned.substring(6)}`;
    }
    return phone;
}


function filterEmployees(department) {
    if (department === 'all') {
        filteredEmployees = [...employees];
    } else {
        filteredEmployees = employees.filter(employee =>
            employee.oddeleni.includes(department)
        );
    }

    // Abecední seřazení podle příjmení
    sortEmployeesAlphabetically(filteredEmployees);

    generateEmployeeHTML();
    updateEmployeeNumbers();
}


const displayEmployees = (values) => {
    const normalizedValues = removeDiacritics(values.toUpperCase());

    // Získáme aktivní oddělení z bočního panelu
    const activeDepartmentElement = document.querySelector('#departmentsListDirectory .department-item.active');
    const activeDepartment = activeDepartmentElement ? activeDepartmentElement.getAttribute('data-department') : 'all';

    filteredEmployees = employees.filter(employee => {
        const name = removeDiacritics(employee.jmeno.toUpperCase());
        const position = removeDiacritics(employee.pozice.toUpperCase());
        const department = removeDiacritics(employee.oddeleni.toUpperCase());

        const matchesSearch = name.includes(normalizedValues) ||
                            position.includes(normalizedValues) ||
                            department.includes(normalizedValues);

        // Filtrování podle vybraného oddělení z bočního panelu
        const matchesFilter = activeDepartment === 'all' ||
                            employee.oddeleni.split(',').map(d => d.trim()).includes(activeDepartment);

        return matchesSearch && matchesFilter;
    });

    // Vždy abecední řazení podle příjmení
    sortEmployeesAlphabetically(filteredEmployees);

    generateEmployeeHTML();
    updateEmployeeNumbers();
};




function updateEmployeeNumbers() {
    const filterHeader = document.querySelector('.filter-header');

    if (!filterHeader) {
        return;
    }

    // Skryjeme počet zaměstnanců podle požadavku
    filterHeader.innerHTML = '';

    // Aktualizace počítadel v directory search header
    const directoryVisibleCount = document.getElementById('directoryVisibleCount');
    const directoryTotalCount = document.getElementById('directoryTotalCount');

    if (directoryVisibleCount && directoryTotalCount && employees && filteredEmployees) {
        directoryVisibleCount.textContent = filteredEmployees.length;
        directoryTotalCount.textContent = employees.length;
    }
}

function removeDiacritics(str) {
    return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
}

function addSpinEffect() {
    // Odstraněna rotace - pouze hover efekt zvětšení je nyní v CSS
    // Funkce ponechána pro kompatibilitu, ale neimplementuje rotaci
}

function initializeSearchPlaceholder() {
    const searchInput = document.getElementById('searchEmployee');
    const placeholders = [
        "Vyhledat zaměstnance...",
        "Zadejte jméno nebo příjmení...",
        "Hledat podle pozice...",
        "Najít kolegu z oddělení...",
        "Například: Novák, vedoucí..."
    ];

    let currentPlaceholderIndex = 0;
    let placeholderIndex = 0;
    let isDeleting = false;

    function animatePlaceholder() {
        const currentText = placeholders[currentPlaceholderIndex];

        if (!isDeleting && placeholderIndex <= currentText.length) {
            searchInput.placeholder = currentText.substring(0, placeholderIndex);
            placeholderIndex++;
            setTimeout(animatePlaceholder, 80);
        } else if (isDeleting && placeholderIndex >= 0) {
            searchInput.placeholder = currentText.substring(0, placeholderIndex);
            placeholderIndex--;
            setTimeout(animatePlaceholder, 40);
        } else if (!isDeleting && placeholderIndex > currentText.length) {
            setTimeout(() => {
                isDeleting = true;
                animatePlaceholder();
            }, 2000);
        } else if (isDeleting && placeholderIndex < 0) {
            isDeleting = false;
            currentPlaceholderIndex = (currentPlaceholderIndex + 1) % placeholders.length;
            setTimeout(animatePlaceholder, 500);
        }
    }

    animatePlaceholder();

    searchInput.addEventListener('focus', () => {
        searchInput.placeholder = 'Začněte psát...';
        searchInput.style.backgroundColor = '#fff';
        searchInput.style.borderColor = '#00add0';
    });

    searchInput.addEventListener('blur', () => {
        if (searchInput.value === '') {
            placeholderIndex = 0;
            isDeleting = false;
            currentPlaceholderIndex = 0;
            setTimeout(animatePlaceholder, 500);
        }
        searchInput.style.backgroundColor = '';
        searchInput.style.borderColor = '';
    });
}

// Old search functionality removed - now handled in DOMContentLoaded

const filterButtons = document.querySelectorAll('.filter-btn');

filterButtons.forEach(button => {
    button.addEventListener('click', () => {
        filterButtons.forEach(btn => btn.classList.remove('active'));
        
        button.classList.add('active');
        
        const department = button.getAttribute('data-filter');
        filterEmployees(department);
    });
});

const modal = document.getElementById('employeeModal');
const closeBtn = document.querySelector('.close');

function closeModal() {
    modal.classList.add('closing');
    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('closing');
    }, 300);
}

closeBtn.onclick = closeModal;

window.onclick = (event) => {
    if (event.target == modal) {
        closeModal();
    }
};

// Theme toggle functionality
const themeToggleBtn = document.getElementById('themeToggle');
const themeIcon = document.getElementById('themeIcon');
const currentTheme = localStorage.getItem('theme');

// Initialize theme
if (currentTheme === 'dark') {
    document.body.classList.add('dark-mode');
    themeIcon.className = 'fas fa-moon';
} else {
    document.body.classList.remove('dark-mode');
    themeIcon.className = 'fas fa-sun';
}

function toggleTheme() {
    const isDark = document.body.classList.contains('dark-mode');

    if (isDark) {
        document.body.classList.remove('dark-mode');
        themeIcon.className = 'fas fa-sun';
        localStorage.setItem('theme', 'light');
    } else {
        document.body.classList.add('dark-mode');
        themeIcon.className = 'fas fa-moon';
        localStorage.setItem('theme', 'dark');
    }
}

themeToggleBtn.addEventListener('click', toggleTheme);



function showErrorMessage(message) {
    const employeeListContainer = document.querySelector('.employee_listing');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        background: #fee;
        border: 1px solid #fcc;
        color: #c33;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
        text-align: center;
    `;
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        <p>${message}</p>
    `;

    const departmentFilters = employeeListContainer.querySelector('.department-filters');
    if (departmentFilters) {
        departmentFilters.insertAdjacentElement('afterend', errorDiv);
    } else {
        employeeListContainer.appendChild(errorDiv);
    }
}



document.addEventListener('DOMContentLoaded', () => {
    loadEmployeeData().then(data => {
        console.log('Data úspěšně načtena:', data.length, 'zaměstnanců');
        employees = data;
        filteredEmployees = [...employees];
        
        // Zpřístupnění dat globálně pro map-integration.js a našeptávání
        window.employeesData = employees;
        window.employees = employees;
        
        // Abecední seřazení podle příjmení při načtení (všichni zaměstnanci)
        sortEmployeesAlphabetically(filteredEmployees);
        
        initializeDirectoryDepartmentsPanel();
        generateEmployeeHTML();
        updateEmployeeNumbers();
        updateDirectoryDepartmentTitle('all'); // Inicializace názvu oddělení

    }).catch(error => {
        console.error('Chyba při načítání dat ze zamestnanci.json:', error);
        showErrorMessage(`Nepodařilo se načíst data zaměstnanců: ${error.message}`);
        // Pokud načítání JSON selže, použijeme fallback na hardcoded data
        employees = getHardcodedEmployeesData();
        filteredEmployees = [...employees];
        
        // Zpřístupnění dat globálně pro map-integration.js a našeptávání
        window.employeesData = employees;
        window.employees = employees;
        
        // Abecední seřazení podle příjmení při načtení (všichni zaměstnanci)
        sortEmployeesAlphabetically(filteredEmployees);
        
        initializeDirectoryDepartmentsPanel();
        generateEmployeeHTML();
        updateEmployeeNumbers();
        updateDirectoryDepartmentTitle('all'); // Inicializace názvu oddělení

    });
});

// Funkce pro zobrazení zaměstnance na mapě
function showEmployeeOnMap(employeeName) {
    // Zavřeme modální okno
    closeModal();

    // Přepneme na mapu
    if (typeof switchToMap === 'function') {
        switchToMap();

        // Počkáme chvilku na načtení mapy a pak zvýrazníme zaměstnance
        setTimeout(() => {
            if (typeof window.highlightEmployeeOnMap === 'function') {
                window.highlightEmployeeOnMap(employeeName);
            }
        }, 500);
    }
}

// Funkce pro aktualizaci názvu oddělení v unified search component
function updateDirectoryDepartmentTitle(department) {
    const directoryCurrentSection = document.getElementById('directoryCurrentSection');
    if (directoryCurrentSection) {
        if (department === 'all') {
            directoryCurrentSection.textContent = 'Všichni zaměstnanci';
        } else {
            directoryCurrentSection.textContent = department;
        }
    }
}



// Event listenery pro vyhledávání v directory sekci
document.addEventListener('DOMContentLoaded', function() {
    const directorySearchInput = document.getElementById('directorySearchInput');
    const directorySearchClearBtn = document.getElementById('directorySearchClearBtn');

    if (directorySearchInput) {
        console.log('Directory search initialized successfully');

        // Vyhledávání při psaní - pouze filtrování
        directorySearchInput.addEventListener('input', function() {
            const searchTerm = this.value;
            console.log('Directory search term:', searchTerm);

            // Filtrování zaměstnanců podle jména, příjmení, pozice, oddělení
            filterDirectoryEmployees(searchTerm.toLowerCase());
            
            // Zobrazit notifikaci pokud nejsou nalezeni žádní zaměstnanci
            setTimeout(() => {
                const visibleCards = document.querySelectorAll('#employee-grid .employee[style="display: block"]');
                if (searchTerm.trim() && visibleCards.length === 0) {
                    showDirectoryNotFoundMessage(searchTerm);
                }
            }, 300);

            // Zobrazit/skrýt clear button
            if (directorySearchClearBtn) {
                if (searchTerm.length > 0) {
                    directorySearchClearBtn.style.display = 'block';
                } else {
                    directorySearchClearBtn.style.display = 'none';
                }
            }
        });
    } else {
        console.error('Directory search input not found');
    }

    if (directorySearchClearBtn) {
        directorySearchClearBtn.addEventListener('click', function() {
            if (directorySearchInput) {
                directorySearchInput.value = '';
                filterDirectoryEmployees('');
                this.style.display = 'none';
                directorySearchInput.focus();
            }
        });
    }

    // Event listenery pro mapu
    const mapSearchInput = document.getElementById('mapSearchInput');
    const mapSearchClearBtn = document.getElementById('mapSearchClearBtn');

    if (mapSearchInput) {
        // Vyhledávání při psaní - pouze filtrování zaměstnanců
        mapSearchInput.addEventListener('input', function() {
            const searchTerm = this.value;

            // Zobrazit/skrýt clear button
            if (mapSearchClearBtn) {
                if (searchTerm.length > 0) {
                    mapSearchClearBtn.style.display = 'block';
                } else {
                    mapSearchClearBtn.style.display = 'none';
                }
            }

            // Filtrování zaměstnanců v mapě (pouze jména) - simulujeme input event
            const mapSearchInput = document.getElementById('mapSearchInput');
            if (mapSearchInput) {
                mapSearchInput.value = searchTerm;
                // Trigger input event to activate existing map search functionality
                const inputEvent = new Event('input', { bubbles: true });
                mapSearchInput.dispatchEvent(inputEvent);
            }
        });
    }

    if (mapSearchClearBtn) {
        mapSearchClearBtn.addEventListener('click', function() {
            if (mapSearchInput) {
                mapSearchInput.value = '';
                this.style.display = 'none';
                mapSearchInput.focus();

                // Reset filtru v mapě - vyčistíme search input a triggerujeme event
                const mapSearchInput = document.getElementById('mapSearchInput');
                if (mapSearchInput) {
                    mapSearchInput.value = '';
                    const inputEvent = new Event('input', { bubbles: true });
                    mapSearchInput.dispatchEvent(inputEvent);
                }
            }
        });
    }
});

// Funkce pro odstranění diakritiky pro lepší vyhledávání
function removeDiacritics(str) {
    if (!str) return '';
    return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

// Vylepšená funkce pro filtrování zaměstnanců v directory
function filterDirectoryEmployees(searchTerm) {
    const employeeCards = document.querySelectorAll('#employee-grid .employee');
    let visibleCount = 0;
    
    // Pokud je vyhledávací termín prázdný, zobrazíme všechny zaměstnance
    if (!searchTerm || searchTerm.trim() === '') {
        employeeCards.forEach(card => {
            card.style.display = 'block';
            visibleCount++;
        });
        updateDirectorySearchCount(visibleCount, employeeCards.length);
        return;
    }

    // Normalizace vyhledávacího termínu
    const normalizedSearchTerm = removeDiacritics(searchTerm.toLowerCase().trim());
    console.log('Directory search for:', normalizedSearchTerm);

    employeeCards.forEach(card => {
        let isVisible = false;
        
        // Získání všech relevantních textů z karty
        const name = card.querySelector('.employee_name')?.textContent || '';
        const position = card.querySelector('.position')?.textContent || '';
        const department = card.querySelector('.job_type')?.textContent || '';
        
        // Pokus o získání dalších informací z dat zaměstnance - vylepšené hledání
        const employee = employees.find(emp => {
            const cardName = name.trim();
            return emp.jmeno === cardName || emp.jmeno.includes(cardName) || cardName.includes(emp.jmeno);
        });
        
        let email = '';
        let phone = '';
        let mobile = '';
        
        if (employee) {
            email = employee.email || '';
            phone = employee.telefon || '';
            mobile = employee.mobil || '';
        }

        // Vytvoření seznamu všech prohledávaných textů
        const searchableTexts = [
            name,
            position,
            department,
            email,
            phone,
            mobile
        ];

        // Normalizace všech textů
        const normalizedTexts = searchableTexts.map(text => 
            removeDiacritics(text.toLowerCase())
        );

        // Rozdělení jména na části (příjmení, jméno)
        const nameParts = name.trim().split(/\s+/);
        const normalizedNameParts = nameParts.map(part => 
            removeDiacritics(part.toLowerCase())
        );

        // Přidání částí jména do seznamu prohledávaných textů
        normalizedTexts.push(...normalizedNameParts);

        // Přidání částí oddělení pro lepší vyhledávání (včetně čárek pro více oddělení)
        const departmentParts = department.split(',').map(d => d.trim());
        const normalizedDepartmentParts = departmentParts.map(part =>
            removeDiacritics(part.toLowerCase())
        );
        normalizedTexts.push(...normalizedDepartmentParts);

        // Chytré vyhledávání - více způsobů hledání
        // 1. Přesná shoda nebo obsahuje hledaný text
        for (const text of normalizedTexts) {
            if (text.includes(normalizedSearchTerm)) {
                isVisible = true;
                break;
            }
        }

        // 2. Hledání podle začátku slov
        if (!isVisible) {
            for (const text of normalizedTexts) {
                const words = text.split(/\s+/);
                for (const word of words) {
                    if (word.startsWith(normalizedSearchTerm)) {
                        isVisible = true;
                        break;
                    }
                }
                if (isVisible) break;
            }
        }

        // 3. Hledání podle částí jména (např. "petr" najde "Petr Novák")
        if (!isVisible) {
            const fullName = removeDiacritics(name.toLowerCase());
            const searchWords = normalizedSearchTerm.split(/\s+/);

            for (const searchWord of searchWords) {
                if (searchWord.length >= 2 && fullName.includes(searchWord)) {
                    isVisible = true;
                    break;
                }
            }
        }

        // 4. Hledání podle pozice a oddělení současně
        if (!isVisible) {
            const combinedText = removeDiacritics((position + ' ' + department).toLowerCase());
            if (combinedText.includes(normalizedSearchTerm)) {
                isVisible = true;
            }
        }

        // 5. Fuzzy matching pro překlepy (pouze pro delší termíny)
        if (!isVisible && normalizedSearchTerm.length > 2) {
            for (const text of normalizedTexts) {
                if (fuzzyMatch(text, normalizedSearchTerm)) {
                    isVisible = true;
                    break;
                }
            }
        }

        // Nastavení viditelnosti
        if (isVisible) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    // Aktualizace počítadla
    updateDirectorySearchCount(visibleCount, employeeCards.length);
}

// Pomocná funkce pro fuzzy matching (toleruje 1 chybu)
function fuzzyMatch(text, searchTerm) {
    if (searchTerm.length < 3) return false;
    
    // Jednoduchý fuzzy matching - zkontroluje, jestli text obsahuje většinu znaků
    const searchChars = searchTerm.split('');
    let matchCount = 0;
    
    for (const char of searchChars) {
        if (text.includes(char)) {
            matchCount++;
        }
    }
    
    // Pokud se shoduje alespoň 80% znaků, považujeme to za shodu
    return (matchCount / searchChars.length) >= 0.8;
}

// Pomocná funkce pro aktualizaci počítadla
function updateDirectorySearchCount(visibleCount, totalCount) {
    const directoryVisibleCount = document.getElementById('directoryVisibleCount');
    const directoryTotalCount = document.getElementById('directoryTotalCount');

    if (directoryVisibleCount) {
        directoryVisibleCount.textContent = visibleCount;
    }
    if (directoryTotalCount) {
        directoryTotalCount.textContent = totalCount;
    }
}

// Funkce pro zobrazení notifikace o nenalezení zaměstnanců
function showDirectoryNotFoundMessage(searchTerm) {
    // Odstraníme předchozí notifikaci pokud existuje
    const existingMessage = document.querySelector('.directory-not-found-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Vytvoříme novou notifikaci
    const message = document.createElement('div');
    message.className = 'directory-not-found-message';
    message.innerHTML = `
        <i class="fas fa-search"></i>
        <span>Nenalezen zaměstnanec: "<strong>${searchTerm}</strong>"</span>
    `;

    // Přidáme do DOM
    document.body.appendChild(message);

    // Zobrazíme s animací
    setTimeout(() => {
        message.classList.add('show');
    }, 10);

    // Skryjeme po 3 sekundách
    setTimeout(() => {
        message.classList.remove('show');
        setTimeout(() => {
            if (message.parentNode) {
                message.remove();
            }
        }, 300);
    }, 3000);
}

// Zpřístupníme funkci globálně
window.showEmployeeOnMap = showEmployeeOnMap;
window.updateDirectoryDepartmentTitle = updateDirectoryDepartmentTitle;
window.showDirectoryNotFoundMessage = showDirectoryNotFoundMessage;